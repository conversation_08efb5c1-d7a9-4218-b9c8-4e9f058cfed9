#!/usr/bin/env node

/**
 * JSON comparison tool that ignores tiny floating-point precision differences
 * while detecting any meaningful differences between JS and Perl outputs
 */

import fs from 'fs';

const FLOAT_TOLERANCE = 1e-6; // Tolerance for floating-point comparison (much larger than the 1e-14 differences we see)
const COORDINATE_TOLERANCE = 1e-6; // More lenient tolerance for coordinates in point strings

export function isCoordinateString(str) {
  // Check if string looks like coordinates: "x1,y1 x2,y2" or "x1,y1,x2,y2"
  return parseCoordinates(str).length > 0;
}

export function coordinateStringsMatch(js, perl) {
  try {
    // Parse coordinates from both strings
    const jsCoords = parseCoordinates(js);
    const perlCoords = parseCoordinates(perl);
    
    if (jsCoords.length !== perlCoords.length) {
      return {jsLength: jsCoords.length, perlLength: perlCoords.length, issue: 'COORDS_LENGTH_MISMATCH'};
    }
    
    // Compare each coordinate with tolerance
    for (let i = 0; i < jsCoords.length; i++) {
      const diff = Math.abs(jsCoords[i] - perlCoords[i]);
    
      if (diff > COORDINATE_TOLERANCE) {
        return {match: false, issue: 'COORDS_PRECISION_DIFF'};
      }
    }
    
    return {match: true};
  } catch (e) {
    return {match: false, issue: 'COORDS_PARSE_ERROR'}; // If parsing fails, fall back to exact string comparison
  }
}

export function parseCoordinates(str) {
  // Split by spaces, commas, and letters, filter out empty strings and NaNs
  return str.split(/[\s,A-Za-z]+/).filter(s => s.length > 0).map(Number).filter(n => !isNaN(n));
}

export function compare(js, perl, path = '', issues = []) {
  // Handle undefined values
  if (js === undefined && perl === undefined) return issues;
  if (js === undefined || perl === undefined) {
    issues.push({
      type: 'UNDEFINED_MISMATCH',
      path,
      jsValue: js,
      perlValue: perl
    });
    return issues;
  }

  // Handle null values
  if (js === null && perl === null) return issues;
  if (js === null || perl === null) {
    issues.push({
      type: 'NULL_MISMATCH',
      path,
      jsValue: js,
      perlValue: perl
    });
    return issues;
  }

  // Type comparison (must be after null checks since typeof null === 'object')
  if (typeof js !== typeof perl) {
    // Special case for position fields where types may differ but values are equivalent
    if (js == perl && path.endsWith('position')) {
      return issues;
    }
    
    issues.push({
      type: 'TYPE_MISMATCH',
      path,
      jsValue: js,
      perlValue: perl,
      jsType: typeof js,
      perlType: typeof perl
    });
    return issues;
  }

  // Number comparison with tolerance
  if (typeof js === 'number' && typeof perl === 'number') {
    if (isNaN(js) && isNaN(perl)) return issues;
    if (isNaN(js) || isNaN(perl)) {
      issues.push({
        type: 'NAN_MISMATCH',
        path,
        jsValue: js,
        perlValue: perl
      });
      return issues;
    }

    const diff = Math.abs(js - perl);
    
    // Check if difference is significant
    if (diff > FLOAT_TOLERANCE) {
      issues.push({
        type: 'SIGNIFICANT_NUMBER_DIFF',
        path,
        jsValue: js,
        perlValue: perl,
        absoluteDiff: diff,
      });
    }
    return issues;
  }

  // String comparison with special handling for coordinate strings
  if (typeof js === 'string' && typeof perl === 'string') {
    if (js !== perl) {
      // Check if this is a coordinate string (contains numbers separated by commas/spaces)
      if (isCoordinateString(js) && isCoordinateString(perl)) {
        const coordResult = coordinateStringsMatch(js, perl);
        
        if (coordResult.match) {
          return issues; // Coordinates match within tolerance
        }

        if (coordResult.issue === 'COORDS_LENGTH_MISMATCH') {
          issues.push({
            type: 'COORDINATE_LENGTH_MISMATCH',
            path,
            jsLength: coordResult.jsLength,
            perlLength: coordResult.perlLength
          });
        } else if (coordResult.issue === 'COORDS_PRECISION_DIFF') {
          // Skip if precision differences are acceptable
          // issues.push({
          //   type: 'COORDINATE_PRECISION_DIFF',
          //   path,
          //   jsValue: js,
          //   perlValue: perl
          // });
        } else {
          // Parse error or other issue, treat as regular string mismatch
          issues.push({
            type: 'STRING_MISMATCH',
            path,
            jsValue: js,
            perlValue: perl
          });
        }
      } else {
        issues.push({
          type: 'STRING_MISMATCH',
          path,
          jsValue: js,
          perlValue: perl
        });
      }
    }
    return issues;
  }

  // Boolean comparison
  if (typeof js === 'boolean' && typeof perl === 'boolean') {
    if (js !== perl) {
      issues.push({
        type: 'BOOLEAN_MISMATCH',
        path,
        jsValue: js,
        perlValue: perl
      });
    }
    return issues;
  }

  // Array comparison - handles arrays of any depth and complexity
  if (Array.isArray(js) && Array.isArray(perl)) {
    if (js.length !== perl.length) {
      issues.push({
        type: 'ARRAY_LENGTH_MISMATCH',
        path,
        jsLength: js.length,
        perlLength: perl.length
      });
      // Continue comparing up to the shorter length to find other differences
      const minLength = Math.min(js.length, perl.length);
      for (let i = 0; i < minLength; i++) {
        compare(js[i], perl[i], `${path}[${i}]`, issues);
      }
      return issues;
    }

    // Recursively compare each array element
    for (let i = 0; i < js.length; i++) {
      compare(js[i], perl[i], `${path}[${i}]`, issues);
    }
    return issues;
  }

  // Handle array vs non-array type mismatch (this catches cases where one is array, other isn't)
  if (Array.isArray(js) !== Array.isArray(perl)) {
    issues.push({
      type: 'ARRAY_TYPE_MISMATCH',
      path,
      jsIsArray: Array.isArray(js),
      perlIsArray: Array.isArray(perl),
      jsValue: js,
      perlValue: perl
    });
    return issues;
  }

  // Object comparison - handles nested objects of any depth
  if (typeof js === 'object' && typeof perl === 'object') {
    // Both are objects (and not null, not arrays - handled above)
    const jsKeys = Object.keys(js).sort();
    const perlKeys = Object.keys(perl).sort();

    // Check for missing/extra keys
    const missingInJs = perlKeys.filter(key => !jsKeys.includes(key));
    const extraInJs = jsKeys.filter(key => !perlKeys.includes(key));

    if (missingInJs.length > 0) {
      issues.push({
        type: 'MISSING_KEYS_IN_JS',
        path,
        missingKeys: missingInJs
      });
    }

    if (extraInJs.length > 0) {
      issues.push({
        type: 'EXTRA_KEYS_IN_JS',
        path,
        extraKeys: extraInJs
      });
    }

    // Recursively compare all common keys
    const commonKeys = jsKeys.filter(key => perlKeys.includes(key));
    for (const key of commonKeys) {
      const newPath = path ? `${path}.${key}` : key;
      compare(js[key], perl[key], newPath, issues);
    }

    return issues;
  }

  // Handle other primitive types or edge cases
  if (js !== perl) {
    issues.push({
      type: 'VALUE_MISMATCH',
      path,
      jsValue: js,
      perlValue: perl,
      jsType: typeof js,
      perlType: typeof perl
    });
  }

  return issues;
}

export function formatIssue(issue) {
  switch (issue.type) {
    case 'TYPE_MISMATCH':
      return `❌ TYPE MISMATCH at ${issue.path}: JS=${issue.jsType}(${issue.jsValue}), Perl=${issue.perlType}(${issue.perlValue})`;

    case 'UNDEFINED_MISMATCH':
      return `❌ UNDEFINED MISMATCH at ${issue.path}: JS=${issue.jsValue}, Perl=${issue.perlValue}`;

    case 'NULL_MISMATCH':
      return `❌ NULL MISMATCH at ${issue.path}: JS=${issue.jsValue}, Perl=${issue.perlValue}`;

    case 'BOOLEAN_MISMATCH':
      return `❌ BOOLEAN MISMATCH at ${issue.path}: JS=${issue.jsValue}, Perl=${issue.perlValue}`;

    case 'ARRAY_TYPE_MISMATCH':
      return `❌ ARRAY TYPE MISMATCH at ${issue.path}: JS is ${issue.jsIsArray ? 'array' : 'not array'}, Perl is ${issue.perlIsArray ? 'array' : 'not array'}`;

    case 'COORDINATE_LENGTH_MISMATCH':
      return `❌ COORDINATE LENGTH MISMATCH at ${issue.path}: JS=${issue.jsLength} coords, Perl=${issue.perlLength} coords`;

    case 'COORDINATE_PRECISION_DIFF':
      return `⚠️  COORDINATE PRECISION at ${issue.path}: Tiny differences in coordinate precision (likely acceptable)`;
    
    case 'SIGNIFICANT_NUMBER_DIFF':
      return `❌ SIGNIFICANT NUMBER DIFF at ${issue.path}: JS=${issue.jsValue}, Perl=${issue.perlValue} (diff=${issue.absoluteDiff})`;
    
    case 'ARRAY_LENGTH_MISMATCH':
      return `❌ ARRAY LENGTH DIFF at ${issue.path}: JS=${issue.jsLength}, Perl=${issue.perlLength}`;
    
    case 'MISSING_KEYS_IN_JS':
      return `❌ MISSING KEYS IN JS at ${issue.path}: ${issue.missingKeys.join(', ')}`;
    
    case 'EXTRA_KEYS_IN_JS':
      return `❌ EXTRA KEYS IN JS at ${issue.path}: ${issue.extraKeys.join(', ')}`;
        
    case 'STRING_MISMATCH':
      return `❌ STRING MISMATCH at ${issue.path}: JS="${issue.jsValue}", Perl="${issue.perlValue}"`;

    case 'NAN_MISMATCH':
      return `❌ NaN MISMATCH at ${issue.path}: JS=${issue.jsValue}, Perl=${issue.perlValue}`;

    case 'VALUE_MISMATCH':
      return `❌ VALUE MISMATCH at ${issue.path}: JS=${issue.jsType}(${issue.jsValue}), Perl=${issue.perlType}(${issue.perlValue})`;
      
    default:
      return `❌ ${issue.type} at ${issue.path}: JS=${issue.jsValue}, Perl=${issue.perlValue}`;
  }
}

// Main execution
if (process.argv.length < 3) {
  console.error('Usage:');
  console.error('  node smart-compare.js <js-file.json> <perl-file.json>  - Compare two JSON files');
  process.exit(1);
}

if (process.argv.length !== 4) {
  console.error('Usage: node smart-compare.js <js-file.json> <perl-file.json>');
  process.exit(1);
}

const [, , jsFile, perlFile] = process.argv;

try {
  console.log(`📁 JS file: ${jsFile}`);
  console.log(`📁 Perl file: ${perlFile}`);
  console.log(`🎯 Float tolerance: ${FLOAT_TOLERANCE}`);
  console.log('');

  const jsData = JSON.parse(fs.readFileSync(jsFile, 'utf8'));
  const perlData = JSON.parse(fs.readFileSync(perlFile, 'utf8'));

  const issues = compare(jsData, perlData);

  if (issues.length === 0) {
    console.log('✅ PERFECT MATCH! No meaningful differences found.');
    console.log('   (Any differences are below the floating-point tolerance)');
  } else {
    console.log(`❌ Found ${issues.length} meaningful difference(s):`);
    console.log('');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${formatIssue(issue)}`);
    });
  }

  console.log('');
  console.log('📊 Summary:');
  console.log(`   Total comparisons: ${countComparisons(jsData)}`);
  console.log(`   Meaningful differences: ${issues.length}`);
  console.log(`   Match rate: ${((1 - issues.length / Math.max(countComparisons(jsData), 1)) * 100).toFixed(6)}%`);

} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}

export function countComparisons(obj, count = 0) {
  // Handle null and undefined
  if (obj === null || obj === undefined) {
    return count + 1;
  }

  // Handle primitives (string, number, boolean)
  if (typeof obj !== 'object') {
    return count + 1;
  }

  // Handle arrays - recursively count each element
  if (Array.isArray(obj)) {
    return obj.reduce((acc, item) => acc + countComparisons(item), count);
  }

  // Handle objects - recursively count each property value
  return Object.values(obj).reduce((acc, value) => acc + countComparisons(value), count);
}


